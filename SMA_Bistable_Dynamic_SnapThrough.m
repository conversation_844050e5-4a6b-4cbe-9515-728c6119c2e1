% =========================================================================
%               双稳态屈曲梁在时变载荷下的动力学仿真（修正版）
% =========================================================================

function dynamic_bistable_beam_fixed()
    clear;
    close all;
    clc;

    % --- 1. 定义系统参数 ---
    % 几何参数
    L = 100e-3;     % 梁长度 (m)
    b = 20e-3;      % 梁宽度 (m)
    h = 0.4e-3;     % 梁厚度 (m)
    
    % 材料属性 (304不锈钢)
    E = 187.5e9;    % 杨氏模量 (Pa)
    rho = 8000;     % 密度 (kg/m^3)
    
    % 截面性质
    I = b * h^3 / 12; % 截面惯性矩 (m^4)
    A = b * h;        % 截面面积 (m^2)
    EI = E * I;       % 抗弯刚度 (Nm^2)
    
    % 预压缩参数
    P_cr = (2*pi)^2 * EI / L^2;  % 第一模态的欧拉屈曲载荷
    
    % 设置压缩力为临界载荷的倍数
    compression_factor = 1.5;  % 压缩力是临界载荷的1.5倍
    P = compression_factor * P_cr;
    
    fprintf('========== 系统参数 ==========\n');
    fprintf('梁长度 L = %.1f mm\n', L*1000);
    fprintf('梁厚度 h = %.2f mm\n', h*1000);
    fprintf('临界屈曲载荷 P_cr = %.2f N\n', P_cr);
    fprintf('实际压缩载荷 P = %.2f N (%.1f×P_cr)\n', P, compression_factor);
    fprintf('===============================\n\n');
    
    % --- 2. 定义模态振型函数 ---
    phi1 = @(x) (1 - cos(2*pi*x/L));
    
    % --- 3. 建立能量模型 ---
    % 计算系数
    k_bend = EI * (2*pi/L)^4 * L/2;  % 弯曲刚度
    k_comp = -P * (2*pi/L)^2 * L/2;  % 压缩软化
    
    % 总的二次项系数
    a2 = k_bend + k_comp;
    
    % 四次项系数（几何非线性）
    a4 = EI * (2*pi/L)^4 * 3*pi^2/(4*L);
    
    fprintf('能量系数:\n');
    fprintf('a2 = %.2e (应该<0 for bistability)\n', a2);
    fprintf('a4 = %.2e (应该>0 for stability)\n\n', a4);
    
    % 势能函数
    U_func = @(q) a2*q^2/2 + a4*q^4/4;
    dU_dq = @(q) a2*q + a4*q^3;
    
    % 找到稳定平衡点
    if a2 < 0
        q_stable = sqrt(-a2/a4);
        fprintf('稳定平衡点: q = ±%.2f mm\n', q_stable*1000);
        fprintf('对应的中心挠度: ±%.2f mm\n\n', q_stable*phi1(L/2)*1000);
    else
        error('系统不是双稳态！增加压缩力。');
    end
    
    % --- 4. 质量矩阵 ---
    M = rho * A * integral(@(x) phi1(x).^2, 0, L);
    fprintf('模态质量 M = %.4e kg\n', M);
    
    % --- 5. 阻尼 ---
    omega_n = sqrt(abs(a2)/M);
    zeta = 0.01;  % 减小阻尼比到0.01
    C = 2 * zeta * M * omega_n;
    fprintf('自然频率 f_n = %.1f Hz\n', omega_n/(2*pi));
    fprintf('阻尼系数 C = %.4e Ns/m\n\n', C);
    
    % --- 6. 外部载荷（关键修改） ---
    % 方案1：使用阶跃力（突然施加，保持足够长时间）
    use_step_force = true;
    
    if use_step_force
        % 阶跃力方案
        t_start = 0.5;     % 力开始时间
        t_duration = 5.0;  % 力持续时间（增加到2秒）
        
        % 计算所需的力
        U_barrier = U_func(0) - U_func(q_stable);
        F_critical = abs(U_barrier) / (q_stable * phi1(L/2));
        F_max = 2.5 * F_critical;  % 使用2.5倍临界力
        
        F_t = @(t) F_max * (t >= t_start & t <= t_start + t_duration);
        
        t_final = t_start + t_duration + 2.0;  % 总仿真时间
        
    else
        % 方案2：缓慢增加的力（准静态）
        t_ramp_up = 2.0;    % 缓慢增加
        t_hold = 2.0;       % 保持时间
        t_ramp_down = 2.0;  % 缓慢减小
        
        U_barrier = U_func(0) - U_func(q_stable);
        F_critical = abs(U_barrier) / (q_stable * phi1(L/2));
        F_max = 2.0 * F_critical;
        
        F_t = @(t) F_max * ( ...
            (t <= t_ramp_up) .* (t/t_ramp_up) + ...
            (t > t_ramp_up & t <= t_ramp_up + t_hold) .* 1 + ...
            (t > t_ramp_up + t_hold & t <= t_ramp_up + t_hold + t_ramp_down) .* ...
            (1 - (t - t_ramp_up - t_hold)/t_ramp_down) ...
        );
        
        t_final = t_ramp_up + t_hold + t_ramp_down + 2.0;
    end
    
    fprintf('势垒高度: %.2e J\n', abs(U_barrier));
    fprintf('临界翻转力: %.2f N\n', F_critical);
    fprintf('实际施加力: %.2f N (%.1f×临界力)\n\n', F_max, F_max/F_critical);
    
    % --- 7. 动力学方程 ---
    ode_fun_1dof = @(t, Y) [
        Y(2);  % dq/dt = q_dot
        (phi1(L/2) * F_t(t) - C * Y(2) - dU_dq(Y(1))) / M
    ];
    
    % --- 8. 求解 ---
    Y0 = [-q_stable; 0];  % 从负的稳定点开始
    t_span = [0, t_final];
    
    fprintf('开始求解（总时间: %.1f s）...\n', t_final);
    options = odeset('RelTol', 1e-6, 'AbsTol', 1e-8, 'MaxStep', 0.001);
    [T, Y_sol] = ode45(ode_fun_1dof, t_span, Y0, options);
    fprintf('求解完成。共 %d 个时间步。\n\n', length(T));
    
    % --- 9. 后处理 ---
    q1_t = Y_sol(:, 1);
    q1_dot_t = Y_sol(:, 2);
    y_center_t = q1_t * phi1(L/2);
    F_applied_t = arrayfun(F_t, T);
    
    % 计算能量
    U_t = arrayfun(U_func, q1_t);
    KE_t = 0.5 * M * q1_dot_t.^2;
    
    % --- 10. 可视化 ---
    figure('Name', '双稳态梁动力学响应', 'Position', [50, 50, 1500, 900]);
    
    % 子图1: 时间历程
    subplot(2, 3, 1);
    yyaxis left;
    plot(T, y_center_t * 1000, 'b-', 'LineWidth', 2);
    hold on;
    plot([0, t_final], [q_stable*phi1(L/2)*1000, q_stable*phi1(L/2)*1000], 'k--', 'LineWidth', 1);
    plot([0, t_final], [-q_stable*phi1(L/2)*1000, -q_stable*phi1(L/2)*1000], 'k--', 'LineWidth', 1);
    ylabel('中心点挠度 (mm)');
    ylim([-1.5*q_stable*phi1(L/2)*1000, 1.5*q_stable*phi1(L/2)*1000]);
    
    yyaxis right;
    plot(T, F_applied_t, 'r-', 'LineWidth', 1.5);
    ylabel('作用力 (N)');
    
    xlabel('时间 (s)');
    title('动态响应');
    grid on;
    legend('挠度', '稳定位置1', '稳定位置2', '力', 'Location', 'best');
    
    % 子图2: 相图
    subplot(2, 3, 2);
    plot(q1_t * 1000, q1_dot_t * 1000, 'g-', 'LineWidth', 1.5);
    hold on;
    plot(q1_t(1)*1000, q1_dot_t(1)*1000, 'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'r');
    plot(q1_t(end)*1000, q1_dot_t(end)*1000, 'bs', 'MarkerSize', 10, 'MarkerFaceColor', 'b');
    % 标记稳定点
    plot(q_stable*1000, 0, 'kx', 'MarkerSize', 12, 'LineWidth', 2);
    plot(-q_stable*1000, 0, 'kx', 'MarkerSize', 12, 'LineWidth', 2);
    xlabel('模态坐标 q_1 (mm)');
    ylabel('模态速度 dq_1/dt (mm/s)');
    title('相空间轨迹');
    grid on;
    legend('轨迹', '起点', '终点', '稳定点', 'Location', 'best');
    
    % 子图3: 势能和能量
    subplot(2, 3, 3);
    q_plot = linspace(-1.5*q_stable, 1.5*q_stable, 200);
    U_plot = arrayfun(U_func, q_plot);
    
    plot(q_plot*1000, U_plot*1e3, 'k-', 'LineWidth', 2);
    hold on;
    plot(q1_t*1000, U_t*1e3, 'r.', 'MarkerSize', 2);
    xlabel('模态坐标 q_1 (mm)');
    ylabel('势能 (mJ)');
    title('势能曲线');
    grid on;
    legend('势能曲线', '系统轨迹', 'Location', 'best');
    
    % 子图4: 力-位移曲线
    subplot(2, 3, 4);
    plot(y_center_t * 1000, F_applied_t, 'b-', 'LineWidth', 1.5);
    xlabel('中心点挠度 (mm)');
    ylabel('作用力 (N)');
    title('力-位移曲线');
    grid on;
    
    % 子图5: 能量历程
    subplot(2, 3, 5);
    plot(T, U_t*1e3, 'b-', 'LineWidth', 1.5);
    hold on;
    plot(T, KE_t*1e3, 'r-', 'LineWidth', 1.5);
    plot(T, (U_t + KE_t)*1e3, 'k--', 'LineWidth', 1.5);
    xlabel('时间 (s)');
    ylabel('能量 (mJ)');
    title('能量演化');
    legend('势能', '动能', '总能量', 'Location', 'best');
    grid on;
    
    % 子图6: 梁形状动画
    subplot(2, 3, 6);
    x_vec = linspace(0, L, 101) * 1000;
    
    % 初始化
    y_beam = q1_t(1) * phi1(linspace(0, L, 101)) * 1000;
    
    h_line = plot(x_vec, y_beam, 'b-', 'LineWidth', 3);
    hold on;
    
    % 固定端标记
    plot([0, L*1000], [0, 0], 'k^', 'MarkerSize', 12, 'MarkerFaceColor', 'k');
    
    % 中心点
    h_center = plot(L*500, y_beam(51), 'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'r');
    
    % 参考线（两个稳定位置）
    y_stable_pos = q_stable * phi1(linspace(0, L, 101)) * 1000;
    y_stable_neg = -q_stable * phi1(linspace(0, L, 101)) * 1000;
    plot(x_vec, y_stable_pos, 'k--', 'LineWidth', 1);
    plot(x_vec, y_stable_neg, 'k--', 'LineWidth', 1);
    
    % 设置坐标轴
    axis_limit = 1.5 * q_stable * phi1(L/2) * 1000;
    axis([-5, L*1000+5, -axis_limit, axis_limit]);
    xlabel('位置 (mm)');
    ylabel('挠度 (mm)');
    grid on;
    
    h_title = title(sprintf('t = %.3f s, F = %.1f N', T(1), F_applied_t(1)));
    
    % 动画
    fprintf('运行动画...\n');
    skip = max(1, floor(length(T)/200));
    
    for k = 1:skip:length(T)
        y_beam = q1_t(k) * phi1(linspace(0, L, 101)) * 1000;
        
        set(h_line, 'YData', y_beam);
        set(h_center, 'YData', y_beam(51));
        set(h_title, 'String', sprintf('t = %.3f s, F = %.1f N, y = %.1f mm', ...
            T(k), F_applied_t(k), y_center_t(k)*1000));
        
        drawnow;
        
        % 在关键时刻暂停
        if abs(y_center_t(k)*1000) < 1  % 接近零位置时
            pause(0.05);
        end
    end
    
    % --- 11. 结果统计 ---
    fprintf('\n========== 仿真结果 ==========\n');
    fprintf('初始挠度: %.2f mm\n', y_center_t(1)*1000);
    fprintf('最终挠度: %.2f mm\n', y_center_t(end)*1000);
    
    % 检查是否成功翻转
    if abs(y_center_t(end) - q_stable*phi1(L/2)) < 0.1*q_stable*phi1(L/2)
        fprintf('成功翻转到正稳定位置！\n');
    elseif abs(y_center_t(end) + q_stable*phi1(L/2)) < 0.1*q_stable*phi1(L/2)
        if sign(y_center_t(1)) ~= sign(y_center_t(end))
            fprintf('梁越过势垒但返回到原稳定位置\n');
        else
            fprintf('梁未能翻转，保持在原稳定位置\n');
        end
    else
        fprintf('梁处于不稳定状态\n');
    end
    
    fprintf('最大位移: %.2f mm\n', max(y_center_t)*1000);
    fprintf('最小位移: %.2f mm\n', min(y_center_t)*1000);
    fprintf('最大速度: %.1f mm/s\n', max(abs(q1_dot_t))*1000);
    
    % 计算梁是否越过了中心位置
    if max(y_center_t) > 0 && min(y_center_t) < 0
        fprintf('梁越过了中心位置（y=0）\n');
    end
    
    fprintf('===============================\n');
end

function result = iff(condition, true_val, false_val)
    if condition
        result = true_val;
    else
        result = false_val;
    end
end