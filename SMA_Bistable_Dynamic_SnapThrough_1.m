%% SMA驱动的双稳态梁动态Snap-Through分析
% 功能：分析包含SMA滞回效应的动态snap-through过程
% 核心：集成真实滞回模型到动态系统，观察动态跳跃现象
% 作者：热忆阻器系统分析
% 日期：2024

clear all; close all; clc;

%% 系统参数设置
fprintf('=== SMA-双稳态梁动态Snap-Through分析 ===\n');

% 双稳态梁参数（优化为数值稳定版本）
m_eff = 0.01;       % 等效质量 [kg]
c = 0.1;            % 阻尼系数 [N·s/m] - 增大阻尼提高稳定性
a = 100.0;          % 势能系数 [N/m³] - 减小以提高数值稳定性
b = 10.0;           % 势能系数 [N/m] - 线性刚度

% SMA弹簧参数
H = 0.1;            % 弹簧安装高度 [m]
L_0 = 0.08;         % 弹簧初始长度 [m]
k_M = 50.0;         % 马氏体相刚度 [N/m]
k_A = 200.0;        % 奥氏体相刚度 [N/m] - 减小刚度差异提高稳定性

% 相变温度 [°C]
M_s = 50;  M_f = 40;    % 马氏体相变开始/结束温度
A_s = 60;  A_f = 70;    % 奥氏体相变开始/结束温度

% 优化的仿真参数 - 平衡速度和稳定性
dt = 0.01;          % 时间步长 [s] - 适中步长确保稳定性
t_total = 15;       % 总仿真时间 [s] - 适中时间观察完整周期
tspan = [0, t_total];

% 温度循环参数
T_base = 55;        % 基础温度 [°C]
T_amplitude = 20;   % 温度振幅 [°C] - 减小振幅提高稳定性
T_period = 7.5;     % 温度周期 [s] - 适中周期

fprintf('系统参数:\n');
fprintf('  双稳态梁: m=%.4f kg, c=%.3f N·s/m\n', m_eff, c);
fprintf('  势能系数: a=%.1f N/m³, b=%.1f N/m\n', a, b);
fprintf('  稳定点: ±%.4f m\n', sqrt(b/a));
fprintf('  SMA刚度范围: %.1f - %.1f N/m\n', k_M, k_A);
fprintf('  温度循环: %.1f ± %.1f °C, 周期 %.1f s\n', T_base, T_amplitude, T_period);

%% 初始条件设置
q0 = -sqrt(b/a);    % 初始位移（负稳定点）
qd0 = 0;            % 初始速度
y0 = [q0; qd0];     % 初始状态向量

fprintf('初始条件: q0=%.4f m, qd0=%.4f m/s\n', q0, qd0);

%% 清理持久化变量（确保干净的开始）
clear system_ode_with_hysteresis

%% 温度函数定义
temperature_cycle = @(t) T_base + T_amplitude * sin(2*pi*t/T_period);

%% 主要动态仿真
fprintf('\n正在进行动态仿真（包含SMA滞回）...\n');

% 平衡速度和稳定性的ODE求解器选项
options = odeset('RelTol', 1e-5, 'AbsTol', 1e-7, 'MaxStep', 0.1, ...
                'InitialStep', 0.005, 'Stats', 'on');

% 求解动态方程
tic;
[t, y] = ode45(@(t,y) system_ode_with_hysteresis(t, y, m_eff, c, a, b, H, L_0, ...
               M_s, M_f, A_s, A_f, k_M, k_A, temperature_cycle), tspan, y0, options);
solve_time = toc;

fprintf('仿真完成，用时: %.2f 秒\n', solve_time);
fprintf('时间步数: %d\n', length(t));

% 提取位移和速度
q = y(:,1);
qd = y(:,2);

% 计算对应的温度历史
T_history = arrayfun(temperature_cycle, t);

% 后处理：计算SMA刚度历史（用于分析）
k_history = zeros(size(t));
fprintf('正在计算SMA刚度历史...\n');

for i = 1:length(t)
    if i == 1
        T_prev = T_history(1) - 1;  % 初始假设为降温状态
    else
        T_prev = T_history(i-1);
    end
    k_history(i) = calculate_SMA_stiffness_hysteresis(T_history(i), T_prev, ...
                   M_s, M_f, A_s, A_f, k_M, k_A);
end

%% 改进的Snap-Through事件检测
fprintf('正在检测Snap-Through事件...\n');

% 多重检测标准
velocity_threshold = 0.05;  % 速度阈值 [m/s] - 降低阈值提高敏感性
displacement_change_threshold = 0.015;  % 位移变化阈值 [m]
acceleration_threshold = 5.0;  % 加速度阈值 [m/s²]

% 计算加速度（数值微分）
qdd_numeric = gradient(qd, t);

% 检测方法1：高速度检测
high_velocity_mask = abs(qd) > velocity_threshold;

% 检测方法2：高加速度检测
high_accel_mask = abs(qdd_numeric) > acceleration_threshold;

% 检测方法3：位移梯度检测
dq_dt = gradient(q, t);
high_gradient_mask = abs(dq_dt) > velocity_threshold;

% 综合检测：任一条件满足
snap_candidate_mask = high_velocity_mask | high_accel_mask | high_gradient_mask;
snap_candidate_indices = find(snap_candidate_mask);

% 检测snap-through事件
snap_events = [];
if ~isempty(snap_candidate_indices)
    % 使用更智能的分组方法
    event_groups = [];
    if length(snap_candidate_indices) > 0
        current_group = [snap_candidate_indices(1)];

        for i = 2:length(snap_candidate_indices)
            % 如果时间间隔小于0.5秒，认为是同一事件
            time_gap = t(snap_candidate_indices(i)) - t(snap_candidate_indices(i-1));
            if time_gap <= 0.5
                current_group = [current_group, snap_candidate_indices(i)];
            else
                event_groups{end+1} = current_group;
                current_group = [snap_candidate_indices(i)];
            end
        end
        event_groups{end+1} = current_group;
    end

    % 分析每个事件组
    for i = 1:length(event_groups)
        group = event_groups{i};
        start_idx = group(1);
        end_idx = group(end);

        % 扩展事件边界（向前后各扩展几个点）
        start_idx = max(1, start_idx - 5);
        end_idx = min(length(t), end_idx + 5);

        % 检查位移变化幅度
        displacement_change = abs(q(end_idx) - q(start_idx));
        max_velocity = max(abs(qd(start_idx:end_idx)));
        max_acceleration = max(abs(qdd_numeric(start_idx:end_idx)));

        % 多重验证条件
        is_snap_through = (displacement_change > displacement_change_threshold) || ...
                         (max_velocity > velocity_threshold * 2) || ...
                         (max_acceleration > acceleration_threshold * 1.5);

        if is_snap_through
            % 找到事件中的最大速度点
            [~, max_vel_idx] = max(abs(qd(start_idx:end_idx)));
            max_vel_idx = start_idx + max_vel_idx - 1;

            snap_events = [snap_events; [start_idx, end_idx, t(start_idx), t(end_idx), ...
                          q(start_idx), q(end_idx), T_history(max_vel_idx), ...
                          max_velocity, displacement_change]];
        end
    end
end

fprintf('检测到 %d 个Snap-Through事件\n', size(snap_events,1));

% 输出详细的snap-through信息
if ~isempty(snap_events)
    fprintf('\nSnap-Through事件详情:\n');
    fprintf('编号  时间范围[s]      位移变化[mm]  最大速度[m/s]  触发温度[°C]\n');
    fprintf('----  -----------     -----------  -----------   -----------\n');
    for i = 1:size(snap_events,1)
        fprintf('%2d    %.2f-%.2f      %8.2f     %8.3f      %8.1f\n', ...
                i, snap_events(i,3), snap_events(i,4), ...
                snap_events(i,9)*1000, snap_events(i,8), snap_events(i,7));
    end
end

%% 创建详细可视化
fprintf('正在生成动态分析图表...\n');

% 主分析图
figure('Name', 'SMA双稳态梁动态Snap-Through分析', 'Position', [100 100 1600 1000]);

% 子图1: 位移vs时间
subplot(3,3,1);
plot(t, q*1000, 'b-', 'LineWidth', 1.5);
hold on;

% 标记snap-through事件
for i = 1:size(snap_events,1)
    plot(snap_events(i,3:4), [snap_events(i,5), snap_events(i,6)]*1000, ...
         'r-', 'LineWidth', 3);
    plot(snap_events(i,3), snap_events(i,5)*1000, 'ro', 'MarkerSize', 8, ...
         'MarkerFaceColor', 'r');
end

xlabel('时间 [s]');
ylabel('位移 [mm]');
title('位移随时间变化');
grid on;
legend('位移轨迹', '检测到的Snap-Through', 'Snap-Through起点', 'Location', 'best');

% 子图2: 温度vs时间
subplot(3,3,2);
plot(t, T_history, 'g-', 'LineWidth', 1.5);
hold on;

% 标记相变温度
ylims = ylim;
plot([0 t_total], [M_f M_f], 'k--', 'LineWidth', 1);
plot([0 t_total], [M_s M_s], 'k--', 'LineWidth', 1);
plot([0 t_total], [A_s A_s], 'r--', 'LineWidth', 1);
plot([0 t_total], [A_f A_f], 'r--', 'LineWidth', 1);

% 标记snap-through发生时的温度
for i = 1:size(snap_events,1)
    plot(snap_events(i,3), snap_events(i,7), 'ro', 'MarkerSize', 8, ...
         'MarkerFaceColor', 'r');
end

xlabel('时间 [s]');
ylabel('温度 [°C]');
title('温度循环');
grid on;

% 子图3: 关键的位移-温度相图（动态滞回环）
subplot(3,3,3);
plot(T_history, q*1000, 'b-', 'LineWidth', 1.5);
hold on;

% 添加snap-through轨迹
for i = 1:size(snap_events,1)
    event_indices = snap_events(i,1):snap_events(i,2);
    plot(T_history(event_indices), q(event_indices)*1000, 'r-', 'LineWidth', 3);
    plot(T_history(snap_events(i,1)), q(snap_events(i,1))*1000, 'ro', ...
         'MarkerSize', 8, 'MarkerFaceColor', 'r');
    plot(T_history(snap_events(i,2)), q(snap_events(i,2))*1000, 'go', ...
         'MarkerSize', 8, 'MarkerFaceColor', 'g');
end

% 添加方向指示箭头
mid_idx = round(length(t)/4);
if mid_idx > 1 && mid_idx < length(t)
    dT = T_history(mid_idx+1) - T_history(mid_idx-1);
    dq = q(mid_idx+1) - q(mid_idx-1);
    quiver(T_history(mid_idx), q(mid_idx)*1000, dT*5, dq*5000, 'k', ...
           'LineWidth', 1.5, 'MaxHeadSize', 0.3);
end

xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('动态位移-温度轨迹');
grid on;
legend('动态轨迹', 'Snap-Through过程', '起点', '终点', 'Location', 'best');

% 子图4: SMA刚度vs时间
subplot(3,3,4);
plot(t, k_history, 'm-', 'LineWidth', 1.5);
xlabel('时间 [s]');
ylabel('SMA刚度 [N/m]');
title('SMA刚度随时间变化');
grid on;

% 子图5: 速度vs时间
subplot(3,3,5);
plot(t, qd, 'c-', 'LineWidth', 1.5);
hold on;
plot([0 t_total], [velocity_threshold velocity_threshold], 'r--', 'LineWidth', 1);
plot([0 t_total], [-velocity_threshold -velocity_threshold], 'r--', 'LineWidth', 1);

xlabel('时间 [s]');
ylabel('速度 [m/s]');
title('速度随时间变化');
grid on;
legend('速度', '检测阈值', 'Location', 'best');

% 子图6: 相空间轨迹
subplot(3,3,6);
plot(q*1000, qd, 'b-', 'LineWidth', 1);
hold on;

% 标记snap-through在相空间中的轨迹
for i = 1:size(snap_events,1)
    event_indices = snap_events(i,1):snap_events(i,2);
    plot(q(event_indices)*1000, qd(event_indices), 'r-', 'LineWidth', 3);
end

xlabel('位移 [mm]');
ylabel('速度 [m/s]');
title('相空间轨迹');
grid on;

% 子图7: 能量分析
subplot(3,3,7);
% 计算动能、势能和总能量
KE = 0.5 * m_eff * qd.^2;  % 动能
PE = (a/4)*q.^4 - (b/2)*q.^2;  % 双稳态势能
SMA_PE = 0.5 * k_history .* (H - q - L_0).^2;  % SMA弹性势能
Total_E = KE + PE + SMA_PE;

plot(t, KE, 'r-', 'LineWidth', 1.5, 'DisplayName', '动能');
hold on;
plot(t, PE, 'b-', 'LineWidth', 1.5, 'DisplayName', '双稳态势能');
plot(t, SMA_PE, 'g-', 'LineWidth', 1.5, 'DisplayName', 'SMA势能');
plot(t, Total_E, 'k--', 'LineWidth', 2, 'DisplayName', '总能量');

xlabel('时间 [s]');
ylabel('能量 [J]');
title('能量随时间变化');
legend('Location', 'best');
grid on;

% 子图8: SMA应变分析
subplot(3,3,8);
SMA_strain = (H - q - L_0) / L_0 * 100;  % 百分比应变
plot(t, SMA_strain, 'purple', 'LineWidth', 1.5);
xlabel('时间 [s]');
ylabel('SMA应变 [%]');
title('SMA弹簧应变');
grid on;

% 子图9: 温度-刚度关系
subplot(3,3,9);
plot(T_history, k_history, 'orange', 'LineWidth', 1.5);
xlabel('温度 [°C]');
ylabel('SMA刚度 [N/m]');
title('温度-刚度滞回关系');
grid on;

% 保存主分析图
if ~exist('SMA_Dynamic_Results', 'dir')
    mkdir('SMA_Dynamic_Results');
end
saveas(gcf, fullfile('SMA_Dynamic_Results', 'SMA_Dynamic_SnapThrough_Analysis.png'));
saveas(gcf, fullfile('SMA_Dynamic_Results', 'SMA_Dynamic_SnapThrough_Analysis.fig'));

%% Snap-Through事件详细分析
if ~isempty(snap_events)
    fprintf('\n正在生成Snap-Through事件详细分析...\n');
    
    figure('Name', 'Snap-Through事件详细分析', 'Position', [200 200 1400 900]);
    
    for event_idx = 1:min(4, size(snap_events,1))  % 最多分析前4个事件
        subplot(2,2,event_idx);
        
        % 提取事件周围的数据
        start_idx = max(1, snap_events(event_idx,1) - 50);
        end_idx = min(length(t), snap_events(event_idx,2) + 50);
        
        t_event = t(start_idx:end_idx);
        q_event = q(start_idx:end_idx);
        T_event = T_history(start_idx:end_idx);
        
        % 双y轴图
        yyaxis left;
        plot(t_event, q_event*1000, 'b-', 'LineWidth', 2);
        ylabel('位移 [mm]');
        
        yyaxis right;
        plot(t_event, T_event, 'r-', 'LineWidth', 2);
        ylabel('温度 [°C]');
        
        % 标记snap-through区间
        hold on;
        event_start = snap_events(event_idx,3);
        event_end = snap_events(event_idx,4);
        xlims = xlim;
        yyaxis left;
        ylims = ylim;
        fill([event_start event_end event_end event_start], ...
             [ylims(1) ylims(1) ylims(2) ylims(2)], 'yellow', 'FaceAlpha', 0.3);
        
        xlabel('时间 [s]');
        title(sprintf('Snap-Through事件 %d (t=%.2f-%.2fs)', ...
              event_idx, event_start, event_end));
        grid on;
    end
    
    saveas(gcf, fullfile('SMA_Dynamic_Results', 'SnapThrough_Events_Detail.png'));
    saveas(gcf, fullfile('SMA_Dynamic_Results', 'SnapThrough_Events_Detail.fig'));
end

%% 与静态分析的对比
fprintf('\n正在生成与静态分析的对比...\n');

% 计算静态滞回环（复用静态分析的逻辑）
T_static = linspace(min(T_history), max(T_history), 200);
q_static_heating = zeros(size(T_static));
q_static_cooling = zeros(size(T_static));

% 升温路径
T_prev = T_static(1) - 5;
q_prev = 0.05;
for i = 1:length(T_static)
    k = calculate_SMA_stiffness_hysteresis(T_static(i), T_prev, ...
        M_s, M_f, A_s, A_f, k_M, k_A);
    coeffs = [a, 0, -(b + k), k*(H - L_0)];
    q_static_heating(i) = solve_equilibrium_equation(coeffs, q_prev);
    T_prev = T_static(i);
    q_prev = q_static_heating(i);
end

% 降温路径
T_static_cool = flip(T_static);
T_prev = T_static_cool(1) + 5;
q_prev = q_static_heating(end);
for i = 1:length(T_static_cool)
    k = calculate_SMA_stiffness_hysteresis(T_static_cool(i), T_prev, ...
        M_s, M_f, A_s, A_f, k_M, k_A);
    coeffs = [a, 0, -(b + k), k*(H - L_0)];
    q_static_cooling(i) = solve_equilibrium_equation(coeffs, q_prev);
    T_prev = T_static_cool(i);
    q_prev = q_static_cooling(i);
end
T_static_cool = flip(T_static_cool);
q_static_cooling = flip(q_static_cooling);

% 对比图
figure('Name', '动态vs静态对比分析', 'Position', [300 300 1200 800]);

subplot(2,2,1);
plot(T_static, q_static_heating*1000, 'r--', 'LineWidth', 2, 'DisplayName', '静态升温');
hold on;
plot(T_static_cool, q_static_cooling*1000, 'b--', 'LineWidth', 2, 'DisplayName', '静态降温');
plot(T_history, q*1000, 'k-', 'LineWidth', 1, 'DisplayName', '动态轨迹');

% 标记snap-through
for i = 1:size(snap_events,1)
    event_indices = snap_events(i,1):snap_events(i,2);
    plot(T_history(event_indices), q(event_indices)*1000, 'g-', 'LineWidth', 3);
end

xlabel('温度 [°C]');
ylabel('位移 [mm]');
title('动态轨迹 vs 静态滞回环');
legend('Location', 'best');
grid on;

% 其他对比分析子图...
subplot(2,2,2);
plot(t, T_history, 'LineWidth', 1.5);
xlabel('时间 [s]');
ylabel('温度 [°C]');
title('温度历史');
grid on;

subplot(2,2,3);
plot(t, q*1000, 'LineWidth', 1.5);
xlabel('时间 [s]');
ylabel('位移 [mm]');
title('位移历史');
grid on;

subplot(2,2,4);
if ~isempty(snap_events)
    bar(1:size(snap_events,1), snap_events(:,7));
    xlabel('Snap-Through事件编号');
    ylabel('触发温度 [°C]');
    title('Snap-Through触发温度');
    grid on;
end

saveas(gcf, fullfile('SMA_Dynamic_Results', 'Dynamic_vs_Static_Comparison.png'));

%% 输出分析报告
fprintf('\n=== 动态Snap-Through分析结果 ===\n');
fprintf('仿真时间: %.1f s，时间步数: %d\n', t_total, length(t));
fprintf('检测到Snap-Through事件: %d 个\n', size(snap_events,1));

if ~isempty(snap_events)
    fprintf('\nSnap-Through事件详情:\n');
    for i = 1:size(snap_events,1)
        fprintf('事件 %d: t=%.3f-%.3fs, 位移变化=%.2fmm, 触发温度=%.1f°C\n', ...
                i, snap_events(i,3), snap_events(i,4), ...
                (snap_events(i,6)-snap_events(i,5))*1000, snap_events(i,7));
    end
end

fprintf('\n结果已保存到 SMA_Dynamic_Results 文件夹\n');
fprintf('分析完成!\n');

%% 函数定义部分

function dydt = system_ode_with_hysteresis(t, y, m_eff, c, a, b, H, L_0, ...
                                          M_s, M_f, A_s, A_f, k_M, k_A, temperature_cycle)
    % 包含SMA滞回效应的动态系统ODE函数 - 保持原有tanh模型

    persistent T_last is_initialized

    % 初始化持久化变量
    if isempty(is_initialized)
        T_last = temperature_cycle(0) - 1;  % 初始假设温度历史
        is_initialized = true;
    end

    % 计算当前温度
    T_current = temperature_cycle(t);

    % 使用原有的tanh滞回模型
    k = calculate_SMA_stiffness_hysteresis(T_current, T_last, ...
        M_s, M_f, A_s, A_f, k_M, k_A);

    % 提取状态变量
    q = y(1);      % 位移
    qd = y(2);     % 速度

    % 计算力
    F_bistable = -a*q^3 + b*q;                    % 双稳态恢复力
    F_SMA = -k*(q - (H - L_0));                   % SMA弹簧力
    F_damping = -c*qd;                            % 阻尼力

    % 动力学方程：m*q_ddot = F_total
    qdd = (F_bistable + F_SMA + F_damping) / m_eff;

    % 输出导数向量
    dydt = [qd; qdd];

    % 更新持久化变量
    T_last = T_current;
end

% 复用静态分析中的函数
function k = calculate_SMA_stiffness_hysteresis(T_current, T_previous, M_s, M_f, A_s, A_f, k_M, k_A)
    if T_current >= T_previous
        xi_A = calculate_austenite_fraction(T_current, A_s, A_f);
        k = k_M + (k_A - k_M) * xi_A;
    else
        xi_M = calculate_martensite_fraction(T_current, M_s, M_f);
        k = k_A - (k_A - k_M) * xi_M;
    end
    k = max(min(k_M, k_A), min(max(k_M, k_A), k));
end

function xi_A = calculate_austenite_fraction(T, A_s, A_f)
    if A_f <= A_s
        xi_A = (T >= A_s);
        return;
    end
    T_mid = (A_s + A_f) / 2;
    T_width = (A_f - A_s) / 4;
    xi_A = 0.5 * (1 + tanh((T - T_mid) / T_width));
    xi_A = max(0, min(1, xi_A));
end

function xi_M = calculate_martensite_fraction(T, M_s, M_f)
    if M_s <= M_f
        xi_M = (T <= M_s);
        return;
    end
    T_mid = (M_s + M_f) / 2;
    T_width = (M_s - M_f) / 4;
    xi_M = 0.5 * (1 + tanh((T_mid - T) / T_width));
    xi_M = max(0, min(1, xi_M));
end

function q = solve_equilibrium_equation(coeffs, q_initial)
    try
        roots_complex = roots(coeffs);
        real_roots = real(roots_complex(abs(imag(roots_complex)) < 1e-10));
        if isempty(real_roots)
            q = q_initial;
            return;
        end
        [~, idx] = min(abs(real_roots - q_initial));
        q = real_roots(idx);
        if isnan(q) || isinf(q) || abs(q) > 1.0
            q = q_initial;
        end
    catch
        q = q_initial;
    end
end

